import requests

import TokenInfo

# Cấu hình API
API_BASE_URL = "https://api.appstorespy.com/v1"
API_KEY = TokenInfo.API_KEY

headers = {
    "API-KEY": API_KEY,
    "accept": "application/json"
}


# LẤY APP DETAILS CƠ BẢN
def get_app_details(app_id, store="play", country="US"):
    """
    Lấy thông tin app
    store: "play" hoặc "ios"
    """
    if store == "play":
        url = f"{API_BASE_URL}/play/apps/{app_id}"
    else:
        url = f"{API_BASE_URL}/ios/apps/{app_id}"

    try:
        response = requests.get(url, headers=headers, params={"country": country})
        response.raise_for_status()
        return response.json()
    except:
        return None


# TÌM KIẾM APPS
def search_apps(query, store="play", country="US", limit=50, sort="-installs_exact"):
    """Tìm kiếm apps"""
    if store == "play":
        url = f"{API_BASE_URL}/play/apps"
    else:
        url = f"{API_BASE_URL}/ios/apps"
        sort = "-downloads" if sort == "-installs_exact" else sort

    params = {
        "q": query,
        "country": country,
        "limit": limit,
        "sort": sort
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except:
        return []


# CHART RANKINGS THEO CATEGORY
def get_category_chart(category, store="play", country="US", limit=50):
    """
    Lấy chart rankings theo category cụ thể

    Categories phổ biến:
    - GAMES: "GAME_ACTION", "GAME_ADVENTURE", "GAME_ARCADE", "GAME_CASUAL", etc.
    - APPS: "SOCIAL", "COMMUNICATION", "ENTERTAINMENT", "PRODUCTIVITY", etc.
    """
    print(f"🎮 Getting {category} Chart - Top {limit}")

    # Tìm kiếm rộng rãi trong category
    results = search_apps("", store=store, country=country, limit=200, sort="-installs_exact")

    if not results:
        return []

    # Filter theo category
    category_apps = []
    rank = 1

    for app in results:
        app_category = app.get('category', '')
        if app_category == category:
            installs_field = 'installs_exact' if store == 'play' else 'downloads'
            installs = app.get(installs_field, 0)
            name = app.get('name', 'N/A')
            rating = app.get('rating_avg', 0)

            category_apps.append({
                'rank': rank,
                'name': name,
                'id': app.get('bundle', app.get('id', 'N/A')),
                'installs': installs,
                'rating': rating,
                'developer': app.get('developer_name', 'N/A')
            })

            print(f"#{rank:2d}. {name} - {installs:,} installs - ⭐{rating}")

            rank += 1
            if rank > limit:
                break

    return category_apps


# GAMES CHART CỤ THỂ
def get_games_chart(game_type="ALL", country="US", limit=50, store="play"):
    """
    Lấy games chart theo type cụ thể

    Game types:
    - "ALL" - Tất cả games
    - "GAME_ACTION" - Action games
    - "GAME_CASUAL" - Casual games
    - "GAME_STRATEGY" - Strategy games
    - "GAME_PUZZLE" - Puzzle games
    - "GAME_ARCADE" - Arcade games
    """
    print(f"🎮 Games Chart: {game_type}")

    if game_type == "ALL":
        # Lấy tất cả games
        results = search_apps("", store=store, country=country, limit=200, sort="-installs_exact")

        games = []
        rank = 1

        for app in results:
            if app.get('type') == 'GAME' or app.get('category', '').startswith('GAME_'):
                installs_field = 'installs_exact' if store == 'play' else 'downloads'
                installs = app.get(installs_field, 0)
                name = app.get('name', 'N/A')
                category = app.get('category', 'N/A')
                rating = app.get('rating_avg', 0)

                games.append({
                    'rank': rank,
                    'name': name,
                    'id': app.get('bundle', app.get('id', 'N/A')),
                    'category': category,
                    'installs': installs,
                    'rating': rating
                })

                print(f"#{rank:2d}. {name} ({category}) - {installs:,} installs - ⭐{rating}")

                rank += 1
                if rank > limit:
                    break

        return games
    else:
        # Lấy game type cụ thể
        return get_category_chart(game_type, store=store, country=country, limit=limit)


# SO SÁNH RANKINGS GIỮA CÁC COUNTRIES
def compare_category_rankings(category, countries=["US", "GB", "DE"], limit=10, store="play"):
    """So sánh rankings của cùng category giữa các countries"""

    all_rankings = {}

    for country in countries:
        print(f"\n🌍 {category} Chart - {country}")
        ranking = get_category_chart(category, store=store, country=country, limit=limit)
        all_rankings[country] = ranking

    return all_rankings


# VÍ DỤ SỬ DỤNG
if __name__ == "__main__":
    # 1. Lấy Games Chart
    print("🎮 TOP GAMES CHART")
    print("=" * 50)
    games_chart = get_games_chart("ALL", country="US", limit=20)

    # 2. Lấy Action Games Chart cụ thể
    print("\n⚔️ ACTION GAMES CHART")
    print("=" * 50)
    action_games = get_games_chart("GAME_ACTION", country="US", limit=15)

    # 3. Lấy Social Apps Chart
    print("\n📱 SOCIAL APPS CHART")
    print("=" * 50)
    social_apps = get_category_chart("SOCIAL", country="US", limit=15)

    # 4. So sánh Games Chart giữa các countries
    print("\n🌍 GAMES CHART COMPARISON")
    print("=" * 50)
    comparison = compare_category_rankings("GAME_CASUAL", ["US", "GB", "DE"], limit=5)

    print("\n✅ DONE!")

    # Available functions:
    # get_category_chart(category, store, country, limit)
    # get_games_chart(game_type, country, limit, store)
    # compare_category_rankings(category, countries, limit, store)