import requests

import TokenInfo

# Cấu hình API
API_BASE_URL = "https://api.appstorespy.com/v1"
API_KEY = TokenInfo.API_KEY

headers = {
    "API-KEY": API_KEY,
    "accept": "application/json"
}


# LẤY APP DETAILS CƠ BẢN
def get_app_details(app_id, store="play", country="US"):
    """
    Lấy thông tin app
    store: "play" hoặc "ios"
    """
    if store == "play":
        url = f"{API_BASE_URL}/play/apps/{app_id}"
    else:
        url = f"{API_BASE_URL}/ios/apps/{app_id}"

    try:
        response = requests.get(url, headers=headers, params={"country": country})
        response.raise_for_status()
        return response.json()
    except:
        return None


# TÌM KIẾM APPS
def search_apps(query, store="play", country="US", limit=50, sort="-installs_exact"):
    """Tìm kiếm apps"""
    if store == "play":
        url = f"{API_BASE_URL}/play/apps/query"
        sort = "-downloads" if sort == "-installs_exact" else sort
    else:
        url = f"{API_BASE_URL}/ios/apps"
        sort = "-downloads" if sort == "-installs_exact" else sort

    params = {
        "q": query,
        "country": country,
        "limit": limit,
        "sort": sort
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except:
        return []


# CHART RANKINGS THEO CATEGORY
def get_category_chart(category, store="play", country="US", limit=50):
    """
    Lấy chart rankings theo category cụ thể

    Categories phổ biến:
    - GAMES: "GAME_ACTION", "GAME_ADVENTURE", "GAME_ARCADE", "GAME_CASUAL", etc.
    - APPS: "SOCIAL", "COMMUNICATION", "ENTERTAINMENT", "PRODUCTIVITY", etc.
    """
    print(f"🎮 Getting {category} Chart - Top {limit}")

    # Tìm kiếm rộng rãi trong category
    results = search_apps("", store=store, country=country, limit=200, sort="-installs_exact")

    if not results:
        return []

    # Filter theo category
    category_apps = []
    rank = 1

    for app in results:
        app_category = app.get('category', '')
        if app_category == category:
            installs_field = 'installs_exact' if store == 'play' else 'downloads'
            installs = app.get(installs_field, 0)
            name = app.get('name', 'N/A')
            rating = app.get('rating_avg', 0)

            category_apps.append({
                'rank': rank,
                'name': name,
                'id': app.get('bundle', app.get('id', 'N/A')),
                'installs': installs,
                'rating': rating,
                'developer': app.get('developer_name', 'N/A')
            })

            print(f"#{rank:2d}. {name} - {installs:,} installs - ⭐{rating}")

            rank += 1
            if rank > limit:
                break

    return category_apps


# GAMES CHART CỤ THỂ
def get_games_chart(game_type="ALL", country="US", limit=50, store="play"):
    """
    Lấy games chart theo type cụ thể

    Game types:
    - "ALL" - Tất cả games
    - "GAME_ACTION" - Action games
    - "GAME_CASUAL" - Casual games
    - "GAME_STRATEGY" - Strategy games
    - "GAME_PUZZLE" - Puzzle games
    - "GAME_ARCADE" - Arcade games
    """
    print(f"🎮 Games Chart: {game_type}")

    if game_type == "ALL":
        # Lấy tất cả games
        results = search_apps("", store=store, country=country, limit=200, sort="-installs_exact")

        games = []
        rank = 1

        for app in results:
            if app.get('type') == 'GAME' or app.get('category', '').startswith('GAME_'):
                installs_field = 'installs_exact' if store == 'play' else 'downloads'
                installs = app.get(installs_field, 0)
                name = app.get('name', 'N/A')
                category = app.get('category', 'N/A')
                rating = app.get('rating_avg', 0)

                games.append({
                    'rank': rank,
                    'name': name,
                    'id': app.get('bundle', app.get('id', 'N/A')),
                    'category': category,
                    'installs': installs,
                    'rating': rating
                })

                print(f"#{rank:2d}. {name} ({category}) - {installs:,} installs - ⭐{rating}")

                rank += 1
                if rank > limit:
                    break

        return games
    else:
        # Lấy game type cụ thể
        return get_category_chart(game_type, store=store, country=country, limit=limit)


# SO SÁNH RANKINGS GIỮA CÁC COUNTRIES
def compare_category_rankings(category, countries=["US", "GB", "DE"], limit=10, store="play"):
    """So sánh rankings của cùng category giữa các countries"""

    all_rankings = {}

    for country in countries:
        print(f"\n🌍 {category} Chart - {country}")
        ranking = get_category_chart(category, store=store, country=country, limit=limit)
        all_rankings[country] = ranking

    return all_rankings


# DEBUG: KIỂM TRA SORT OPTIONS
def check_sort_options(category="GAME_ACTION", limit=5):
    """Kiểm tra các cách sort khác nhau"""

    sort_options = [
        "-installs_exact",  # Total installs
        "-ipd",  # Daily installs
        "-downloads_daily",  # Daily downloads
        "-revenue",  # Revenue
        "-rating_avg",  # Rating
        "-review_count"  # Review count
    ]

    print(f"🔍 Testing different sort methods for {category}:")
    print("=" * 60)

    for sort_method in sort_options:
        print(f"\n📊 Sort by: {sort_method}")
        try:
            results = search_apps("", store="play", country="US", limit=100, sort=sort_method)

            if results:
                category_count = 0
                for app in results:
                    if app.get('category') == category and category_count < limit:
                        name = app.get('name', 'N/A')
                        installs = app.get('installs_exact', 0)
                        ipd = app.get('ipd', 0)
                        rating = app.get('rating_avg', 0)

                        print(f"  #{category_count + 1}. {name}")
                        print(f"      Total: {installs:,} | Daily: {ipd:,} | ⭐{rating}")

                        category_count += 1
            else:
                print(f"  ❌ No results for {sort_method}")
        except Exception as e:
            print(f"  ❌ Error with {sort_method}: {e}")


# CHART RANKINGS THEO DAILY INSTALLS (IPD)
def get_category_chart_by_daily(category, store="play", country="US", limit=50):
    """
    Lấy chart dựa trên DAILY installs (IPD) - gần với real-time hơn
    """
    print(f"📈 Getting {category} Chart by Daily Installs (IPD)")

    # Sort theo daily installs thay vì total
    results = search_apps("", store=store, country=country, limit=200, sort="chart_info")

    if not results:
        print("Failed to get results. Please try again later.")
        return []

    category_apps = []
    rank = 1

    for app in results:
        app_category = app.get('category', '')
        if app_category == category:
            installs = app.get('installs_exact', 0)
            daily_installs = app.get('ipd', 0)  # Installs per day
            name = app.get('name', 'N/A')
            rating = app.get('rating_avg', 0)

            category_apps.append({
                'rank': rank,
                'name': name,
                'id': app.get('bundle', app.get('id', 'N/A')),
                'installs': installs,
                'daily_installs': daily_installs,
                'rating': rating,
                'developer': app.get('developer_name', 'N/A')
            })

            print(f"#{rank:2d}. {name}")
            print(f"       Daily: {daily_installs:,} | Total: {installs:,} | ⭐{rating}")

            rank += 1
            if rank > limit:
                break

    return category_apps


# MULTIPLE SORT COMPARISON
def compare_sort_methods(category="GAME_ACTION", limit=10):
    """So sánh kết quả của các sort methods khác nhau"""

    print(f"⚔️ Comparing Sort Methods for {category}")
    print("=" * 60)

    # Method 1: Total Installs
    print(f"\n📊 Method 1: Sort by Total Installs")
    chart1 = get_category_chart(category, limit=limit)

    # Method 2: Daily Installs
    print(f"\n📈 Method 2: Sort by Daily Installs (IPD)")
    chart2 = get_category_chart_by_daily(category, limit=limit)

    # Method 3: Revenue
    print(f"\n💰 Method 3: Sort by Revenue")
    results = search_apps("", store="play", country="US", limit=200, sort="-revenue")
    rank = 1
    for app in results:
        if app.get('category') == category and rank <= limit:
            name = app.get('name', 'N/A')
            revenue = app.get('revenue', 0)
            installs = app.get('installs_exact', 0)
            print(f"#{rank:2d}. {name} - Revenue: {revenue} | Installs: {installs:,}")
            rank += 1

    return {"total_installs": chart1, "daily_installs": chart2}

# VÍ DỤ SỬ DỤNG
if __name__ == "__main__":
    # 1. Lấy Games Chart
    print("🎮 TOP GAMES CHART")
    print("=" * 50)
    games_chart = get_category_chart_by_daily("GAME_ACTION", country="US", limit=20)

    print("===============================")
    results = search_apps("", store="play", country="US", limit=100, sort="-revenue")

    for app in results:
        app_category = app.get('category', '')
        installs = app.get('installs_exact', 0)
        daily_installs = app.get('ipd', 0)  # Installs per day
        name = app.get('name', 'N/A')
        rating = app.get('rating_avg', 0)
        rank = app.get('chart_info', {}).get('position', 0)

        print(f"#{rank:2d}. {name}")
        print(f"       Daily: {daily_installs:,} | Total: {installs:,} | ⭐{rating}")

        rank += 1

    print("\n✅ DONE!")