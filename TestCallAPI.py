import requests
import json

import TokenInfo

# Cấu hình API - từ documentation chính thức
API_BASE_URL = "https://api.appstorespy.com/v1"
API_KEY = TokenInfo.API_KEY

# Headers cho authentication - theo OpenAPI spec
headers = {
    "API-KEY": API_KEY,  # Đúng format từ documentation!
    "accept": "application/json"
}


# 1. LẤY THÔNG TIN APP CỤ THỂ (Google Play)
def get_play_app_details(app_id, country="US", fields=None):
    """
    Lấy thông tin chi tiết của app từ Google Play

    Args:
        app_id: Package name (vd: com.whatsapp)
        country: Mã quốc gia (US, VN, etc.)
        fields: Cá<PERSON> fields cần lấy, ngăn cách bằng dấu phẩy
    """
    url = f"{API_BASE_URL}/play/apps/{app_id}"

    params = {
        "country": country
    }
    if fields:
        params["fields"] = fields

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error getting app details: {e}")
        return None


# 2. LẤY THÔNG TIN APP CỤ THỂ (iOS/App Store)
def get_ios_app_details(app_id, country="US", fields=None):
    """
    Lấy thông tin chi tiết của app từ App Store

    Args:
        app_id: App Store ID (vd: 333903271)
        country: Mã quốc gia
        fields: Các fields cần lấy
    """
    url = f"{API_BASE_URL}/ios/apps/{app_id}"

    params = {
        "country": country
    }
    if fields:
        params["fields"] = fields

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error getting iOS app details: {e}")
        return None


# 3. TÌM KIẾM APPS (Google Play)
def search_play_apps(query, country="US", limit=10, sort=None):
    """
    Tìm kiếm apps trên Google Play

    Args:
        query: Từ khóa tìm kiếm
        country: Mã quốc gia
        limit: Số kết quả (tối đa 1000)
        sort: Sắp xếp (vd: "-installs_exact", "-revenue")
    """
    url = f"{API_BASE_URL}/play/apps"

    params = {
        "q": query,
        "country": country,
        "limit": limit
    }
    if sort:
        params["sort"] = sort

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error searching apps: {e}")
        return None


# 4. TÌM KIẾM APPS (iOS/App Store)
def search_ios_apps(query, country="US", limit=10, sort=None):
    """
    Tìm kiếm apps trên App Store
    """
    url = f"{API_BASE_URL}/ios/apps"

    params = {
        "q": query,
        "country": country,
        "limit": limit
    }
    if sort:
        params["sort"] = sort

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error searching iOS apps: {e}")
        return None


# 5. LẤY ESTIMATES/REVENUE DATA
def get_play_estimates(app_ids, start_date=None, end_date=None):
    """
    Lấy ước tính downloads/revenue của apps Google Play

    Args:
        app_ids: Danh sách app IDs, ngăn cách bằng dấu phẩy
        start_date: Ngày bắt đầu (YYYY-MM-DD)
        end_date: Ngày kết thúc (YYYY-MM-DD)
    """
    url = f"{API_BASE_URL}/play/estimates"

    params = {
        "id": app_ids  # Có thể là "com.app1,com.app2,com.app3"
    }
    if start_date:
        params["start"] = start_date
    if end_date:
        params["end"] = end_date

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error getting estimates: {e}")
        return None


# 6. LẤY DAILY INSTALLS
def get_daily_installs(app_id, start_date=None, end_date=None):
    """
    Lấy daily installs data cho Google Play app
    """
    url = f"{API_BASE_URL}/play/apps/{app_id}/installs_daily"

    params = {}
    if start_date:
        params["start"] = start_date
    if end_date:
        params["end"] = end_date

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error getting daily installs: {e}")
        return None


# 7. FILTER SEARCH NÂNG CAO (Google Play)
def advanced_play_search(filter_data, limit=10, sort="-downloads_exact", fields=None):
    """
    Tìm kiếm nâng cao với nhiều filter cho Google Play
    """
    url = f"{API_BASE_URL}/play/apps/query"

    search_body = {
        "limit": limit,
        "sort": sort,
        "country": "US",
        "language": "en_US",
        "filter": filter_data
    }

    if fields:
        search_body["fields"] = fields

    try:
        response = requests.post(url, headers=headers, json=search_body)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error with advanced search: {e}")
        return None


# 8. LẤY REVIEWS
def get_app_reviews(app_id, country="US", language="en_US", limit=10, sort="stars"):
    """
    Lấy reviews của app từ Google Play
    """
    url = f"{API_BASE_URL}/play/apps/{app_id}/reviews"

    params = {
        "country": country,
        "language": language,
        "limit": limit,
        "sort": sort
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        data = response.json()
        return data

    except requests.exceptions.RequestException as e:
        print(f"Error getting reviews: {e}")
        return None


# 9. LẤY APPS THEO CHART RANKINGS
def get_top_chart_apps(collection="topselling_free", category="ALL", country="US", limit=50):
    """
    Lấy danh sách apps theo chart rankings bằng cách filter

    Args:
        collection: "topselling_free", "topselling_paid", "topgrossing", etc.
        category: Category của chart (GAMES, SOCIAL, ALL, etc.)
        country: Mã quốc gia
        limit: Số lượng apps
    """
    filter_criteria = {
        "published": True,
        "chart_filter": {
            "collection": collection,
            "country": country,
            "category": category
        }
    }

    # Dùng advanced search để lấy apps có chart info
    results = advanced_play_search(
        filter_criteria,
        limit=limit,
        sort="chart_info",  # Sort theo chart position
        fields=["name", "bundle", "chart_info", "installs_exact", "rating_avg", "developer_name"]
    )

    return results


# 10. TÌM APPS CÓ CHART RANKING CAO
def find_trending_apps(country="US", min_position=1, max_position=100):
    """
    Tìm apps đang trending trên charts
    """
    filter_criteria = {
        "published": True,
        "active_countries": [country],
        # Tìm apps có chart_info (đang trên charts)
    }

    results = advanced_play_search(
        filter_criteria,
        limit=100,
        sort="-chart_info",  # Apps có chart ranking cao hơn
        fields=["name", "bundle", "chart_info", "installs_exact", "rating_avg", "category"]
    )

    # Filter theo position range
    if results and "data" in results:
        trending = []
        for app in results["data"]:
            chart_info = app.get("chart_info")
            if chart_info and isinstance(chart_info, dict):
                position = chart_info.get("position")
                if position and min_position <= position <= max_position:
                    trending.append(app)
        return trending

    return []


# DEBUG & TESTING FUNCTIONS
def test_api_connection():
    """Test cơ bản để kiểm tra API key và connection"""
    print("=== Testing API Connection ===")

    # Test 1: Basic app request không có fields
    print("1. Testing basic app request...")
    try:
        url = f"{API_BASE_URL}/play/apps/com.whatsapp"
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 403:
            print("❌ API Key invalid hoặc không có quyền truy cập")
            return False
        elif response.status_code == 200:
            print("✅ API Key hợp lệ")
            data = response.json()
            print(f"App name: {data.get('name', 'N/A')}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False


def get_available_countries():
    """Lấy danh sách countries được support"""
    url = f"{API_BASE_URL}/play/info/countries"
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error getting countries: {e}")
        return None


def get_available_languages():
    """Lấy danh sách languages được support"""
    url = f"{API_BASE_URL}/play/info/languages"
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error getting languages: {e}")
        return None


def simple_app_search(query="whatsapp", limit=5):
    """Search đơn giản không có filter phức tạp"""
    print(f"\n=== Simple Search: {query} ===")
    url = f"{API_BASE_URL}/play/apps"

    params = {
        "q": query,
        "limit": limit,
        "country": "US"
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data)} apps:")
            for app in data[:3]:
                print(f"- {app.get('name')} ({app.get('bundle')})")
            return data
        else:
            print(f"Error: {response.text}")
            return None

    except Exception as e:
        print(f"Search error: {e}")
        return None


def simple_advanced_search():
    """Test advanced search với filter đơn giản"""
    print("\n=== Simple Advanced Search ===")
    url = f"{API_BASE_URL}/play/apps/query"

    # Filter rất đơn giản
    search_body = {
        "limit": 5,
        "page": 1,
        "country": "US",
        "language": "en_US",
        "filter": {
            "published": True,
            "name": "whatsapp"
        }
    }

    try:
        response = requests.post(url, headers=headers, json=search_body)
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Advanced search works")
            if "data" in data:
                print(f"Found {len(data['data'])} apps")
                for app in data["data"][:2]:
                    print(f"- {app.get('name')} ({app.get('bundle')})")
            return data
        else:
            print(f"❌ Error: {response.text}")
            return None

    except Exception as e:
        print(f"Advanced search error: {e}")
        return None


# VÍ DỤ TESTING & DEBUGGING
if __name__ == "__main__":
    print("🔧 DEBUGGING APPSTORESPY API")
    print("=" * 50)

    # Kiểm tra API key trước
    if not test_api_connection():
        print("\n❌ API connection failed. Check your API_KEY!")
        print("- Lấy API key tại: https://appstorespy.com/account")
        print("- Cần Business Plan để sử dụng API")
        exit()

    # Test các info endpoints
    print("\n=== Getting API Info ===")
    countries = get_available_countries()
    if countries:
        print(f"✅ Supported countries: {len(countries.get('countries', []))}")

    languages = get_available_languages()
    if languages:
        print(f"✅ Supported languages: {len(languages.get('languages', []))}")

    # Test search đơn giản
    search_results = simple_app_search("whatsapp", 3)

    # Test advanced search đơn giản
    advanced_results = simple_advanced_search()

    print("\n" + "=" * 50)
    print("🎯 DEBUGGING RESULTS:")
    print("1. Nếu API connection OK → API key đúng")
    print("2. Nếu simple search OK → basic endpoints work")
    print("3. Nếu advanced search fail → check filter format")
    print("4. Check response để thấy available fields")

    print("\n=== 2. Tìm kiếm apps về 'chat' ===")
    chat_apps = search_play_apps("chat", country="US", limit=5, sort="-installs_exact")
    if chat_apps:
        for app in chat_apps[:5]:
            print(f"- {app.get('name')} ({app.get('bundle')}) - {app.get('installs_exact', 'N/A')} installs")

    print("\n=== 3. Lấy estimates của WhatsApp ===")
    estimates = get_play_estimates("com.whatsapp", start_date="2024-01-01", end_date="2024-12-31")
    if estimates:
        for est in estimates:
            print(f"Month: {est.get('month')} - Downloads: {est.get('downloads', 'N/A')}")

    print("\n=== 4. Tìm kiếm nâng cao - Apps có > 1M downloads ===")
    filter_criteria = {
        "published": True,
        "category_type": "APP",
        "downloads_exact": {"gte": 1000000},  # > 1 triệu installs
        "rating_avg": {"gte": 4.0},  # Rating >= 4.0
        "active_countries": ["US"]
    }

    advanced_results = advanced_play_search(
        filter_criteria,
        limit=5,
        sort="-downloads_exact",
        fields=["name", "bundle", "downloads_exact", "rating_avg", "developer_name"]
    )

    if advanced_results and "data" in advanced_results:
        for app in advanced_results["data"]:
            print(f"- {app.get('name')} - {app.get('downloads_exact'):,} installs - ⭐{app.get('rating_avg')}")

    print("\n=== 5. Lấy reviews của WhatsApp ===")
    reviews = get_app_reviews("com.whatsapp", limit=3, sort="-likes")
    if reviews:
        for review in reviews[:3]:
            print(f"⭐{review.get('stars')} - {review.get('author_name')}: {review.get('comment', '')[:100]}...")

# CÁC FIELDS QUAN TRỌNG CÓ THỂ LẤY:
"""
Google Play App Fields:
- Cơ bản: id, bundle, name, developer_name, category, published
- Installs/Downloads: installs_exact, ipd (daily installs), downloads_month
- Ratings: rating_avg, rating_value, rating_count, review_count  
- Revenue: revenue, revenue_month
- Metadata: description, icon, screenshots, version, size, updated
- Charts: chart_info (vị trí trên charts)
- Ads: advertised, ads

iOS App Fields:
- Tương tự như Play Store nhưng dùng downloads thay vì installs
- Có thêm: devices (iPhone/iPad support), arcade (Apple Arcade)
- revenue, downloads, rating_count, rating_value, rating_avg
"""

# RESPONSE FORMAT EXAMPLES:
"""
App Details Response:
{
  "id": "com.whatsapp",
  "name": "WhatsApp Messenger",
  "developer_name": "WhatsApp Inc.", 
  "installs_exact": 5000000000,
  "rating_avg": 4.1,
  "category": "COMMUNICATION",
  "ipd": 1500000,  // daily installs
  "revenue": 0,
  "chart_info": {
    "position": 1,
    "collection": "topselling_free",
    "category": "COMMUNICATION"
  }
}

Estimates Response:
[
  {
    "id": "com.whatsapp",
    "month": "2024-01",
    "downloads": 45000000,
    "revenue": 0
  }
]
"""